/**
 * AutoPilot AI - 重构版前端应用
 * 
 * 支持两阶段交互模式和TTS播报的现代化界面
 */

class TravelPlannerAppRefactored {
    constructor() {
        this.currentTraceId = null;
        this.eventSource = null;
        this.currentItinerary = null;
        this.currentPhase = 'waiting'; // waiting, analysis, planning, completed
        this.currentUserId = null;
        this.currentQuery = null;

        // 请求锁机制 - 根据推送.md文档要求
        this.isPlanning = false;

        // 动态步骤管理 - 不再硬编码
        this.dynamicSteps = new Map(); // step_id -> step_info

        // 分析步骤状态（保持向后兼容）
        this.analysisSteps = {
            'user_intent': { completed: false, title: '解析用户需求和画像' },
            'poi_preference': { completed: false, title: '景点偏好类型' },
            'food_preference': { completed: false, title: '美食偏好' },
            'accommodation_preference': { completed: false, title: '住宿偏好' }
        };

        // 状态数据存储
        this.state = {
            core_intent: null,
            user_profile: null,
            user_memories: null,
            travel_preferences: null,
            preference_profile: null,
            driving_context: null,
            vehicle_info: null,
            multi_city_strategy: null
        };

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupViewModes();
        this.loadUserHistory();
        this.updateUI();
    }
    
    bindEvents() {
        // 规划表单提交
        document.getElementById('planningForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPlanning();
        });
        
        // 视图模式切换
        document.getElementById('viewModeList').addEventListener('click', () => {
            this.switchViewMode('list');
        });
        
        document.getElementById('viewModeMap').addEventListener('click', () => {
            this.switchViewMode('map');
        });
        
        // 立即规划按钮
        const startPlanningBtn = document.getElementById('startPlanningBtn');
        if (startPlanningBtn) {
            startPlanningBtn.addEventListener('click', () => {
                this.startItineraryPlanning();
            });
        }
        
        // 取消规划按钮
        const cancelPlanningBtn = document.getElementById('cancelPlanningBtn');
        if (cancelPlanningBtn) {
            cancelPlanningBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 行程操作按钮
        document.getElementById('saveItinerary').addEventListener('click', () => {
            this.saveItinerary();
        });
        
        document.getElementById('editItinerary').addEventListener('click', () => {
            this.editItinerary();
        });
        
        document.getElementById('shareItinerary').addEventListener('click', () => {
            this.shareItinerary();
        });
        
        // 历史行程按钮
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });
    }
    
    setupViewModes() {
        // 默认显示列表视图
        this.switchViewMode('list');
    }
    
    updateUI() {
        // 根据当前阶段更新UI显示
        this.hideAllViews();

        switch (this.currentPhase) {
            case 'waiting':
                this.showWaitingView();
                break;
            case 'analysis':
                this.showAnalysisView();
                break;
            case 'planning':
                this.showPlanningView();
                break;
            case 'completed':
                this.showCompletedView();
                break;
        }
    }

    hideAllViews() {
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';
        document.getElementById('itineraryView').style.display = 'none';
    }

    showWaitingView() {
        document.getElementById('waitingView').style.display = 'flex';
    }

    showAnalysisView() {
        document.getElementById('analysisView').style.display = 'flex';

        // 更新分析状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');

        if (title) title.textContent = '正在分析您的需求...';
        if (desc) desc.textContent = 'AI正在理解您的旅行偏好和需求';
    }

    showPlanningView() {
        // 规划阶段：左侧保留意图理解，右侧显示规划过程
        // 隐藏等待和分析状态视图
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';

        // 显示规划视图（在右侧面板中）
        document.getElementById('itineraryView').style.display = 'block';

        // 确保左侧分析面板保持可见（显示已完成的意图理解结果）
        const analysisPanel = document.querySelector('.analysis-panel');
        if (analysisPanel) {
            analysisPanel.style.display = 'block';
        }

        // 在右侧显示规划状态
        this.showPlanningStatus();
    }

    showPlanningStatus() {
        // 在右侧面板显示规划状态
        const itineraryView = document.getElementById('itineraryView');
        if (itineraryView) {
            itineraryView.innerHTML = `
                <div class="planning-status-content">
                    <div class="status-content">
                        <div class="status-spinner">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <h4 class="status-title">🚀 开始规划您的精彩行程...</h4>
                        <p class="status-description">AI正在为您生成个性化的旅行方案</p>
                        <div class="planning-progress">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="planningProgressBar"></div>
                            </div>
                            <div class="progress-text mt-2" id="planningProgressText">准备开始...</div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    showCompletedView() {
        document.getElementById('itineraryView').style.display = 'block';
    }

    // 添加缺失的方法
    regenerateItinerary() {
        if (confirm('确定要重新生成行程吗？')) {
            // 重新启动规划阶段
            this.startPlanningPhase();
        }
    }

    viewItinerary() {
        // 切换到行程查看模式
        this.currentPhase = 'completed';
        this.updateUI();

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('为您展示完整的旅行行程');
        }
    }
    
    async startPlanning() {
        // 1. 加锁检查 - 根据推送.md文档要求
        if (this.isPlanning) {
            console.log("已有规划任务在进行中，请勿重复点击。");
            return;
        }

        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim() || '1';

        if (!query) {
            this.showAlert('请输入您的旅行想法', 'warning');
            return;
        }

        try {
            // 2. 加锁
            this.isPlanning = true;
            const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
            if (planButton) {
                planButton.disabled = true;
                planButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中...';
            }

            // 3. 清理旧的UI状态
            this.clearPreviousResults();

            // 保存当前查询和用户ID
            this.currentQuery = query;
            this.currentUserId = userId;

            // 切换到分析阶段
            this.currentPhase = 'analysis';
            this.updateUI();

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始分析您的旅行需求');
            }

            // 重置分析步骤状态
            this.resetAnalysisSteps();

            // 4. 开始真实的SSE连接
            await this.startRealPlanning(query, userId);

        } catch (error) {
            console.error('规划失败:', error);
            this.showAlert('规划失败: ' + error.message, 'danger');

            // 出现错误时解锁
            this.unlockPlanning();
        }
    }

    clearPreviousResults() {
        // 清理之前的结果
        this.dynamicSteps.clear();
        this.currentItinerary = null;

        // 重置状态数据
        Object.keys(this.state).forEach(key => {
            this.state[key] = null;
        });
    }

    unlockPlanning() {
        // 解锁规划状态
        this.isPlanning = false;
        const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
        if (planButton) {
            planButton.disabled = false;
            planButton.innerHTML = '<i class="bi bi-search"></i> 开始规划';
        }
    }
    
    resetAnalysisSteps() {
        Object.keys(this.analysisSteps).forEach(stepKey => {
            this.analysisSteps[stepKey].completed = false;
            const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
            if (stepElement) {
                stepElement.classList.remove('completed');
                stepElement.classList.remove('active');
                
                // 重置结果显示
                const resultElement = stepElement.querySelector('.analysis-result');
                if (resultElement) {
                    resultElement.innerHTML = '<div class="loading-placeholder">等待分析...</div>';
                }
                
                // 显示加载状态
                const statusElement = stepElement.querySelector('.analysis-status');
                if (statusElement) {
                    statusElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                }
            }
        });
    }
    
    // 模拟分析过程已移除，现在使用真实的SSE连接
    
    setAnalysisStepActive(stepKey) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.add('active');
            stepElement.classList.remove('completed');
        }
    }
    
    completeAnalysisStep(stepKey, content) {
        this.analysisSteps[stepKey].completed = true;
        
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.remove('active');
            stepElement.classList.add('completed');
            
            // 更新结果显示
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = `<div class="analysis-content-text">${content}</div>`;
            }
            
            // 更新状态图标
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
            }
        }
    }
    
    // ==================== 动态UI管理方法 ====================

    showStepInProgress(stepId, title, message) {
        console.log('显示步骤进行中:', stepId, title, message);

        // 1. 动态创建一个新的步骤卡片UI元素
        const stepsContainer = document.getElementById('steps-container') ||
                              document.querySelector('.analysis-steps') ||
                              document.querySelector('.analysis-panel');

        if (!stepsContainer) {
            console.warn('未找到步骤容器');
            return;
        }

        const cardHtml = `
            <div class="step-card" id="step-card-${stepId}">
                <h3 id="title-${stepId}">${title}</h3>
                <p id="message-${stepId}" class="step-message">${message}</p>
                <div class="step-status" id="status-${stepId}">
                    <div class="spinner-border spinner-border-sm" role="status"></div>
                </div>
            </div>
        `;

        stepsContainer.innerHTML += cardHtml;
    }

    showStepSuccess(stepId, result) {
        console.log('显示步骤成功:', stepId, result);

        const statusElement = document.getElementById(`status-${stepId}`);
        if (statusElement) {
            statusElement.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
        }

        const messageElement = document.getElementById(`message-${stepId}`);
        if (messageElement && result) {
            // 根据结果更新消息内容
            const resultText = this.formatStepResult(result);
            messageElement.innerHTML = resultText;
        }

        const cardElement = document.getElementById(`step-card-${stepId}`);
        if (cardElement) {
            cardElement.classList.add('completed');
        }
    }

    showStepError(stepId, errorMessage) {
        console.log('显示步骤错误:', stepId, errorMessage);

        const statusElement = document.getElementById(`status-${stepId}`);
        if (statusElement) {
            statusElement.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i>';
        }

        const messageElement = document.getElementById(`message-${stepId}`);
        if (messageElement) {
            messageElement.innerHTML = `<span class="text-danger">${errorMessage}</span>`;
        }

        const cardElement = document.getElementById(`step-card-${stepId}`);
        if (cardElement) {
            cardElement.classList.add('error');
        }
    }

    updateStepProgress(stepId, progress, message) {
        console.log('更新步骤进度:', stepId, progress, message);

        const messageElement = document.getElementById(`message-${stepId}`);
        if (messageElement) {
            messageElement.innerHTML = `${message} (${progress}%)`;
        }
    }

    formatStepResult(result) {
        // 格式化步骤结果为显示文本
        if (typeof result === 'string') {
            return result;
        } else if (typeof result === 'object') {
            // 简单的对象格式化
            return JSON.stringify(result, null, 2);
        } else {
            return String(result);
        }
    }

    showStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        const cancelBtn = document.getElementById('cancelPlanningBtn');

        if (startBtn) {
            startBtn.style.display = 'block';
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="bi bi-play-circle"></i> 立即规划';
            startBtn.classList.add('animate__animated', 'animate__fadeInUp');
            startBtn.onclick = () => this.startPlanningPhase();
        }

        if (cancelBtn) {
            cancelBtn.style.display = 'inline-block';
        }

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('分析完成，点击立即规划开始生成行程');
        }
    }

    hideStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.style.display = 'none';
        }
    }

    disableStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.disabled = true;
            startBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 规划中...';
        }
    }
    
    async startItineraryPlanning() {
        try {
            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();

            // 隐藏按钮
            document.getElementById('startPlanningBtn').style.display = 'none';

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }

            // 启动第二阶段的规划流程
            await this.startPlanningPhase();

        } catch (error) {
            console.error('行程规划失败:', error);
            this.showAlert('行程规划失败: ' + error.message, 'danger');
        }
    }

    async startPlanningPhase() {
        // 检查是否已经在规划中
        if (this.isPlanning) {
            console.log("规划阶段已在进行中，请勿重复点击。");
            return;
        }

        try {
            // 加锁
            this.isPlanning = true;

            // 禁用立即规划按钮
            this.disableStartPlanningButton();

            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();

            // 关闭之前的SSE连接
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }

            // 准备分析结果数据
            const analysisResult = {
                session_id: this.currentTraceId,
                user_id: this.currentUserId || '1',
                query: this.currentQuery,
                core_intent: this.state.core_intent,
                user_profile: this.state.user_profile,
                user_memories: this.state.user_memories,
                travel_preferences: this.state.travel_preferences,
                preference_profile: this.state.preference_profile,
                driving_context: this.state.driving_context,
                vehicle_info: this.state.vehicle_info,
                multi_city_strategy: this.state.multi_city_strategy
            };

            console.log('启动规划阶段，分析结果:', analysisResult);

            // 使用规划阶段管理器启动规划
            if (window.planningPhaseManager) {
                await window.planningPhaseManager.startPlanningPhase(
                    this.currentTraceId,
                    analysisResult
                );
            } else {
                console.warn('规划阶段管理器未找到，使用降级方案');
                // 降级到原有的SSE连接方式
                await this.startPlanningPhaseFallback();
            }

        } catch (error) {
            console.error('启动规划阶段失败:', error);
            this.showAlert('启动规划失败: ' + error.message, 'danger');

            // 出现错误时解锁
            this.unlockPlanning();
        }
    }

    async startPlanningPhaseFallback() {
        // 原有的SSE连接方式作为降级方案
        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim() || '1';

        const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${userId}&query=${encodeURIComponent(query)}&phase=planning`;
        this.eventSource = new EventSource(url);

        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleSSEEvent(data);
            } catch (error) {
                console.error('解析SSE事件失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.eventSource.close();
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        };
    }

    // 模拟行程生成已移除，现在使用真实的SSE连接
    
    displayItinerary(result) {
        console.log('显示行程数据:', result);

        // 检查数据格式
        if (!result || (!result.itinerary && !result.daily_itineraries)) {
            console.error('行程数据格式错误:', result);
            this.showAlert('行程数据格式错误', 'danger');
            return;
        }

        // 兼容不同的数据格式
        const itineraryData = result.itinerary || result.daily_itineraries || result;
        const summary = result.summary || {};

        // 更新行程标题和描述
        const title = summary.destinations?.join(' → ') || '精彩旅程';
        const description = `${summary.days || itineraryData.length}天${summary.travel_theme || '休闲'}之旅`;

        const titleElement = document.getElementById('itineraryTitle');
        const descElement = document.getElementById('itineraryDescription');

        if (titleElement) titleElement.textContent = title;
        if (descElement) descElement.textContent = description;

        // 更新统计信息
        this.updateItineraryStats(itineraryData, summary);

        // 显示详细行程卡片
        this.displayItineraryCards(itineraryData);

        // 切换到结果视图
        this.currentPhase = 'completed';
        this.updateUI();

        this.currentItinerary = result;
    }

    updateItineraryStats(itineraryData, summary) {
        // 计算统计信息
        const totalDays = itineraryData.length || summary.days || 0;
        let totalPOIs = 0;

        if (Array.isArray(itineraryData)) {
            itineraryData.forEach(day => {
                if (day.time_blocks) {
                    totalPOIs += day.time_blocks.length;
                } else if (day.activities) {
                    totalPOIs += day.activities.length;
                }
            });
        }

        // 更新DOM元素
        const totalDaysElement = document.getElementById('totalDays');
        const totalPOIsElement = document.getElementById('totalPOIs');
        const estimatedBudgetElement = document.getElementById('estimatedBudget');
        const weatherInfoElement = document.getElementById('weatherInfo');

        if (totalDaysElement) totalDaysElement.textContent = totalDays;
        if (totalPOIsElement) totalPOIsElement.textContent = totalPOIs;
        if (estimatedBudgetElement) estimatedBudgetElement.textContent = summary.estimated_budget || '待估算';
        if (weatherInfoElement) weatherInfoElement.textContent = summary.weather || '晴朗';
    }

    displayItineraryCards(itineraryData) {
        console.log('显示行程卡片:', itineraryData);

        if (!Array.isArray(itineraryData)) {
            console.error('行程数据不是数组格式:', itineraryData);
            return;
        }

        // 找到行程容器
        const container = document.querySelector('.itinerary-result') ||
                         document.querySelector('#itineraryView .result-content') ||
                         document.querySelector('#itineraryView');

        if (!container) {
            console.error('未找到行程显示容器');
            return;
        }

        // 生成行程卡片HTML
        const cardsHtml = itineraryData.map((day, index) => {
            return this.generateDayCard(day, index + 1);
        }).join('');

        // 添加操作按钮
        const actionButtonsHtml = `
            <div class="itinerary-actions mt-4">
                <button class="btn btn-primary me-2" onclick="window.app.saveItinerary()">
                    <i class="bi bi-bookmark"></i> 保存行程
                </button>
                <button class="btn btn-outline-primary me-2" onclick="window.app.editItinerary()">
                    <i class="bi bi-pencil"></i> 编辑行程
                </button>
                <button class="btn btn-outline-success" onclick="window.app.shareItinerary()">
                    <i class="bi bi-share"></i> 分享行程
                </button>
            </div>
        `;

        // 更新容器内容
        container.innerHTML = `
            <div class="itinerary-cards">
                ${cardsHtml}
            </div>
            ${actionButtonsHtml}
        `;
    }

    generateDayCard(dayData, dayNumber) {
        // 处理不同的数据格式
        const date = dayData.date || `第${dayNumber}天`;
        const activities = dayData.time_blocks || dayData.activities || [];

        // 生成活动列表
        const activitiesHtml = activities.map(activity => {
            const name = activity.location || activity.name || activity.activity || '未知活动';
            const time = activity.start_time ? `${activity.start_time} - ${activity.end_time}` : '';
            const details = activity.details || activity.description || '';

            return `
                <div class="activity-item mb-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="activity-info">
                            <h6 class="mb-1">${name}</h6>
                            ${details ? `<p class="text-muted small mb-0">${details}</p>` : ''}
                        </div>
                        ${time ? `<span class="badge bg-light text-dark">${time}</span>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="card mb-3 day-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-day me-2"></i>
                        ${date}
                    </h5>
                </div>
                <div class="card-body">
                    ${activitiesHtml || '<p class="text-muted">暂无具体安排</p>'}
                </div>
            </div>
        `;
    }
    
    cancelPlanning() {
        // 停止当前规划
        this.currentPhase = 'waiting';
        this.updateUI();

        // 重置表单
        document.getElementById('userQuery').value = '';

        // 隐藏按钮
        document.getElementById('startPlanningBtn').style.display = 'none';
        document.getElementById('cancelPlanningBtn').style.display = 'none';

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('规划已取消');
        }

        this.showAlert('规划已取消', 'info');
    }

    // 行程操作方法
    saveItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可保存的行程', 'warning');
            return;
        }

        // 这里可以添加保存到本地存储或服务器的逻辑
        localStorage.setItem('saved_itinerary', JSON.stringify(this.currentItinerary));
        this.showAlert('行程已保存到本地', 'success');

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('行程已保存');
        }
    }

    editItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可编辑的行程', 'warning');
            return;
        }

        // 这里可以添加编辑行程的逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }

    shareItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可分享的行程', 'warning');
            return;
        }

        // 生成分享链接或文本
        const shareText = `我的旅行计划：${this.currentItinerary.summary?.destinations?.join(' → ') || '精彩旅程'}`;

        if (navigator.share) {
            navigator.share({
                title: '我的旅行计划',
                text: shareText,
                url: window.location.href
            }).then(() => {
                this.showAlert('分享成功', 'success');
            }).catch(() => {
                this.fallbackShare(shareText);
            });
        } else {
            this.fallbackShare(shareText);
        }
    }

    fallbackShare(text) {
        // 降级分享方案：复制到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showAlert('行程信息已复制到剪贴板', 'success');
            }).catch(() => {
                this.showAlert('分享功能暂不可用', 'warning');
            });
        } else {
            this.showAlert('分享功能暂不可用', 'warning');
        }
    }
    
    switchViewMode(mode) {
        const listBtn = document.getElementById('viewModeList');
        const mapBtn = document.getElementById('viewModeMap');
        const itineraryView = document.getElementById('itineraryView');
        const mapView = document.getElementById('mapView');
        
        if (mode === 'list') {
            listBtn.classList.add('active');
            mapBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'block';
            if (mapView) mapView.style.display = 'none';
        } else {
            mapBtn.classList.add('active');
            listBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'none';
            if (mapView) mapView.style.display = 'block';
        }
    }

    async startRealPlanning(query, userId) {
        // 生成trace_id
        this.currentTraceId = 'trace_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // 创建SSE连接 - 只执行分析阶段
        const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${userId}&query=${encodeURIComponent(query)}&phase=analysis`;
        this.eventSource = new EventSource(url);

        // 处理SSE事件
        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleSSEEvent(data);
            } catch (error) {
                console.error('解析SSE事件失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.eventSource.close();
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        };
    }

    handleSSEEvent(data) {
        console.log('收到SSE事件:', data);

        // 支持新旧两种事件格式
        let eventType, payload;

        if (data.event && data.data) {
            // 新格式：符合推送.md文档要求
            eventType = data.event;
            payload = data.data;
            this.handleNewFormatEvent(eventType, payload);
        } else if (data.event_type && data.payload) {
            // 旧格式：保持向后兼容
            eventType = data.event_type;
            payload = data.payload;
            this.handleLegacyFormatEvent(eventType, payload);
        } else {
            console.warn('未知的事件格式:', data);
        }
    }

    handleNewFormatEvent(eventType, payload) {
        console.log('处理新格式事件:', eventType, payload);

        switch (eventType) {
            case 'step_start':
                this.handleStepStart(payload);
                break;
            case 'step_end':
                this.handleStepEnd(payload);
                break;
            case 'step_progress':
                this.handleStepProgress(payload);
                break;
            case 'completed':
                this.handleCompleted(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未知的新格式事件类型:', eventType);
        }
    }

    handleLegacyFormatEvent(eventType, payload) {
        console.log('处理旧格式事件:', eventType, payload);

        switch (eventType) {
            case 'start':
                this.handleStart(payload);
                break;
            case 'stream_start':
                this.handleStreamStart(payload);
                break;
            case 'thinking_step':
                this.handleThinkingStep(payload);
                break;
            case 'analysis_step':
                this.handleAnalysisStep(payload);
                break;
            case 'tool_call':
                this.handleToolCall(payload);
                break;
            case 'tool_result':
                this.handleToolResult(payload);
                break;
            case 'itinerary_generated':
                this.handleItineraryGenerated(payload);
                break;
            case 'planning_completed':
                this.handlePlanningCompleted(payload);
                break;
            case 'final_itinerary':
                this.handleFinalItinerary(payload);
                break;
            case 'complete':
                this.handleComplete(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未知的旧格式事件类型:', eventType);
        }
    }

    // ==================== 新格式事件处理方法 ====================

    handleStepStart(payload) {
        console.log('处理步骤开始事件:', payload);

        const stepId = payload.step_id;
        const stepName = payload.step_name;
        const title = payload.title;
        const message = payload.message;

        // 保存步骤信息
        this.dynamicSteps.set(stepId, {
            stepName: stepName,
            title: title,
            status: 'running',
            startTime: payload.timestamp
        });

        // 动态创建UI元素
        this.showStepInProgress(stepId, title, message);
    }

    handleStepEnd(payload) {
        console.log('处理步骤结束事件:', payload);

        const stepId = payload.step_id;
        const stepName = payload.step_name;
        const status = payload.status;
        const result = payload.result;
        const message = payload.message;

        // 更新步骤信息
        if (this.dynamicSteps.has(stepId)) {
            const stepInfo = this.dynamicSteps.get(stepId);
            stepInfo.status = status;
            stepInfo.endTime = payload.timestamp;
            stepInfo.result = result;
        }

        if (status === 'success') {
            this.showStepSuccess(stepId, result);

            // 特殊处理：如果是最终完成事件
            if (stepName === 'completed') {
                this.unlockPlanning(); // 解锁
            }
        } else {
            this.showStepError(stepId, message);
            this.unlockPlanning(); // 出现错误，也要解锁
        }
    }

    handleStepProgress(payload) {
        console.log('处理步骤进度事件:', payload);

        const stepId = payload.step_id;
        const progress = payload.progress;
        const message = payload.message;

        // 更新进度显示
        this.updateStepProgress(stepId, progress, message);
    }

    handleCompleted(payload) {
        console.log('处理完成事件:', payload);

        // 保存最终的状态数据
        if (payload.result) {
            this.updateStateData(payload.result);
        }

        // 如果是分析阶段完成，保存分析结果
        if (payload.stage === 'analysis' && payload.analysis_result) {
            console.log('保存分析结果:', payload.analysis_result);

            // 将分析结果保存到state中
            Object.assign(this.state, payload.analysis_result);

            console.log('更新后的state:', this.state);
        }

        // 如果当前是分析阶段，显示立即规划按钮
        if (this.currentPhase === 'analysis') {
            console.log('分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }

        // 关闭当前的SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    // ==================== 旧格式事件处理方法 ====================

    handleStart(payload) {
        console.log('处理开始事件:', payload);
        // 可以在这里显示开始分析的状态
    }

    handleStreamStart(payload) {
        console.log('处理流开始事件:', payload);
        // 可以在这里显示流开始的状态
    }

    handleComplete(payload) {
        console.log('处理完成事件:', payload);

        // 保存最终的状态数据
        if (payload.data) {
            this.updateStateData(payload.data);
        }

        // 如果是分析阶段完成，保存分析结果
        if (payload.stage === 'analysis' && payload.analysis_result) {
            console.log('保存分析结果:', payload.analysis_result);

            // 将分析结果保存到state中
            Object.assign(this.state, payload.analysis_result);

            // 确保关键字段不为空，并从payload.analysis_result中获取
            if (payload.analysis_result.core_intent) {
                this.state.core_intent = payload.analysis_result.core_intent;
            }
            if (payload.analysis_result.user_profile) {
                this.state.user_profile = payload.analysis_result.user_profile;
            }
            if (payload.analysis_result.user_memories) {
                this.state.user_memories = payload.analysis_result.user_memories;
            }
            if (payload.analysis_result.travel_preferences) {
                this.state.travel_preferences = payload.analysis_result.travel_preferences;
            }
            if (payload.analysis_result.preference_profile) {
                this.state.preference_profile = payload.analysis_result.preference_profile;
            }
            if (payload.analysis_result.driving_context) {
                this.state.driving_context = payload.analysis_result.driving_context;
            }
            if (payload.analysis_result.vehicle_info) {
                this.state.vehicle_info = payload.analysis_result.vehicle_info;
            }
            if (payload.analysis_result.multi_city_strategy) {
                this.state.multi_city_strategy = payload.analysis_result.multi_city_strategy;
            }

            console.log('更新后的state:', this.state);
        }

        // 如果当前是分析阶段，显示立即规划按钮
        if (this.currentPhase === 'analysis') {
            console.log('分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }

        // 关闭当前的SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    handleAnalysisStep(payload) {
        // 处理分析步骤事件
        console.log('处理分析步骤:', payload);

        const stepType = payload.step_type;
        const title = payload.title;
        const content = payload.content;
        const completed = payload.completed;

        // 保存状态数据
        if (payload.data) {
            this.updateStateData(payload.data);
        }

        if (stepType && completed) {
            // 更新对应的分析步骤
            this.completeAnalysisStep(stepType, content);

            // 检查是否所有分析步骤都完成了
            const allCompleted = Object.values(this.analysisSteps).every(step => step.completed);
            if (allCompleted) {
                console.log('所有分析步骤完成，显示立即规划按钮');
                this.showStartPlanningButton();
            }
        }
    }

    updateStateData(data) {
        // 更新状态数据
        if (data.core_intent) {
            this.state.core_intent = data.core_intent;
        }
        if (data.user_profile) {
            this.state.user_profile = data.user_profile;
        }
        if (data.user_memories) {
            this.state.user_memories = data.user_memories;
        }
        if (data.travel_preferences) {
            this.state.travel_preferences = data.travel_preferences;
        }
        if (data.preference_profile) {
            this.state.preference_profile = data.preference_profile;
        }
        if (data.driving_context) {
            this.state.driving_context = data.driving_context;
        }
        if (data.vehicle_info) {
            this.state.vehicle_info = data.vehicle_info;
        }
        if (data.multi_city_strategy) {
            this.state.multi_city_strategy = data.multi_city_strategy;
        }

        console.log('状态数据已更新:', this.state);
    }

    handleThinkingStep(payload) {
        // 根据思考步骤更新UI
        console.log('处理思考步骤:', payload);

        // 映射后端的中文分类到前端的步骤
        const categoryMapping = {
            '出行对象': 'user_intent',
            '其他': 'user_intent', // 用户画像分析归类为"其他"，映射到用户需求
            '景点推荐': 'poi_preference',
            '美食推荐': 'food_preference',
            '住宿推荐': 'accommodation_preference'
        };

        const stepKey = categoryMapping[payload.category];
        if (stepKey) {
            this.updateAnalysisStep(stepKey, payload.content);
        } else {
            console.log('未映射的分类:', payload.category, payload.content);
        }

        // 检查是否是分析完成的信号
        if (payload.content && payload.content.includes('分析阶段完成')) {
            console.log('检测到分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }
    }

    handleToolCall(payload) {
        console.log('工具调用:', payload.tool_name, payload.parameters);
    }

    handleToolResult(payload) {
        console.log('工具结果:', payload.tool_name, payload.success);
    }

    handleFinalItinerary(payload) {
        // 显示最终行程
        this.displayRealItinerary(payload);
        this.currentPhase = 'completed';
        this.updateUI();

        // 关闭SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        // TTS播报
        if (window.ttsManager && payload.summary) {
            window.ttsManager.speakItineraryInfo(payload.summary.title, payload.summary.description);
        }
    }

    handleItineraryGenerated(payload) {
        console.log('行程生成中:', payload);

        // 切换到规划阶段视图
        this.currentPhase = 'planning';
        this.updateUI();

        // 更新进度
        const progress = payload.progress || 80;
        this.updatePlanningProgress(progress, '正在生成详细行程...');
    }

    handlePlanningCompleted(payload) {
        console.log('规划完成:', payload);

        // 切换到完成状态
        this.currentPhase = 'completed';
        this.updateUI();

        // 显示完成的行程
        if (payload.summary) {
            this.displayItinerarySummary(payload.summary);
        }
    }

    updatePlanningProgress(progress, message) {
        // 更新规划状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');

        if (title) title.textContent = message || '正在生成旅行方案...';
        if (desc) desc.textContent = `进度: ${progress}%`;
    }

    displayItinerarySummary(summary) {
        console.log('显示行程摘要:', summary);
        // 这里可以添加显示行程摘要的逻辑
        // 暂时显示一个简单的提示
        this.showAlert('行程规划完成！', 'success');
    }

    handleError(payload) {
        console.error('规划错误:', payload);
        this.showAlert('规划过程中发生错误: ' + payload.error_message, 'danger');
        this.currentPhase = 'waiting';
        this.updateUI();

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    updateAnalysisStep(stepKey, content) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            // 标记为活跃状态
            stepElement.classList.add('active');

            // 更新结果内容
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = content;
            }

            // 更新状态为完成
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }

            // 标记为完成
            stepElement.classList.add('completed');
            this.analysisSteps[stepKey].completed = true;

            // 检查是否所有分析步骤都完成了
            const allStepsCompleted = ['user_intent', 'poi_preference', 'food_preference', 'accommodation_preference']
                .every(key => this.analysisSteps[key].completed);

            if (allStepsCompleted) {
                // 显示用户画像
                this.showUserProfile();
            }

            // 检查是否所有步骤都完成了
            const allCompleted = Object.values(this.analysisSteps).every(step => step.completed);
            if (allCompleted) {
                this.showStartPlanningButton();
            }
        }
    }

    showUserProfile() {
        console.log('showUserProfile方法被调用');
        // 用户画像数据已经通过分析步骤显示在四个分析框中
        // 不需要额外的userProfileItem元素
        console.log('用户画像数据已通过分析步骤显示');

        // 从后端获取用户画像数据（如果需要）
        this.fetchUserProfile();
    }

    async fetchUserProfile() {
        console.log('fetchUserProfile方法被调用');
        try {
            console.log('开始获取用户画像数据');
            const response = await fetch('/api/travel/user_profile', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('API响应状态:', response.status);
            if (response.ok) {
                const userProfile = await response.json();
                console.log('获取到用户画像数据:', userProfile);
                this.displayUserProfile(userProfile);
            } else {
                console.error('获取用户画像失败:', response.statusText);
                // 使用模拟数据作为后备
                this.displayUserProfile(this.getMockUserProfile());
            }
        } catch (error) {
            console.error('获取用户画像出错:', error);
            // 使用模拟数据作为后备
            this.displayUserProfile(this.getMockUserProfile());
        }
    }

    getMockUserProfile() {
        return {
            basic_info: {
                age: "25-35岁",
                gender: "女性",
                occupation: "白领",
                travel_companion: "亲子"
            },
            tags: ["慢节奏", "趣味性", "儿童友好", "舒适型"],
            budget_preference: "舒适型",
            preferences: {
                travel_style: "深度游",
                season: "春季",
                duration: "3天"
            },
            recommendation_reason: "根据您的亲子出行需求和舒适型预算偏好，我们为您推荐了适合家庭的景点和活动，确保既有趣味性又能让孩子们开心游玩。"
        };
    }

    displayUserProfile(userProfile) {
        console.log('displayUserProfile方法被调用，数据:', userProfile);

        // 更新基本信息
        const userBasicInfo = document.getElementById('userBasicInfo');
        if (userBasicInfo && userProfile.basic_info) {
            const basicInfo = userProfile.basic_info;
            const infoText = [
                basicInfo.age,
                basicInfo.gender,
                basicInfo.occupation,
                basicInfo.travel_companion
            ].filter(Boolean).join('，');
            userBasicInfo.textContent = infoText || '暂无信息';
        }

        // 更新旅行风格
        const userTravelStyle = document.getElementById('userTravelStyle');
        if (userTravelStyle) {
            userTravelStyle.textContent = userProfile.preferences?.travel_style || userProfile.travel_style || '休闲';
        }

        // 更新兴趣标签
        const userTags = document.getElementById('userTags');
        if (userTags && userProfile.tags) {
            userTags.textContent = userProfile.tags.join('，') || '暂无标签';
        }

        // 更新偏好设置
        const userPreferences = document.getElementById('userPreferences');
        if (userPreferences && userProfile.preferences) {
            const prefs = [];
            if (userProfile.preferences.accommodation_preferences) {
                prefs.push(`住宿：${userProfile.preferences.accommodation_preferences.join('，')}`);
            }
            if (userProfile.preferences.transportation_preferences) {
                prefs.push(`交通：${userProfile.preferences.transportation_preferences.join('，')}`);
            }
            userPreferences.textContent = prefs.join('；') || '暂无特殊偏好';
        }

        // 更新预算偏好
        const userBudget = document.getElementById('userBudget');
        if (userBudget) {
            userBudget.textContent = userProfile.budget_preference || '中等';
        }

        console.log('用户画像显示完成');
    }

    displayBasicInfo(basicInfo) {
        const basicInfoTags = document.getElementById('basicInfoTags');
        if (basicInfoTags && basicInfo) {
            const tags = [];
            if (basicInfo.age) tags.push(basicInfo.age);
            if (basicInfo.gender) tags.push(basicInfo.gender);
            if (basicInfo.occupation) tags.push(basicInfo.occupation);
            if (basicInfo.travel_companion) tags.push(basicInfo.travel_companion);

            basicInfoTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag basic-info">${tag}</span>`
            ).join('');
        }
    }

    displayPreferenceTags(tags) {
        const preferencesTags = document.getElementById('preferencesTags');
        if (preferencesTags && tags.length > 0) {
            preferencesTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag">${tag}</span>`
            ).join('');
        }
    }

    displayBudgetPreference(budgetPreference) {
        const budgetTags = document.getElementById('budgetTags');
        if (budgetTags && budgetPreference) {
            budgetTags.innerHTML = `<span class="profile-tag budget">${budgetPreference}</span>`;
        }
    }

    displayRecommendationReason(reason) {
        const recommendationText = document.getElementById('recommendationText');
        if (recommendationText && reason) {
            recommendationText.textContent = reason;
        }
    }

    displayRealItinerary(itineraryData) {
        // 从真实数据中提取信息
        const summary = itineraryData.summary || {};
        const dailyPlans = itineraryData.daily_plans || [];
        const budgetEstimation = itineraryData.budget_estimation || {};

        // 更新行程标题和描述
        document.getElementById('itineraryTitle').textContent = summary.title || '旅行行程';
        document.getElementById('itineraryDescription').textContent = summary.description || '个性化旅行方案';

        // 更新统计信息
        document.getElementById('totalDays').textContent = summary.days || dailyPlans.length;
        document.getElementById('totalPOIs').textContent = this.countTotalPOIs(dailyPlans);

        // 格式化预算信息
        const budgetText = this.formatBudget(budgetEstimation);
        document.getElementById('estimatedBudget').textContent = budgetText;

        // 更新天气信息
        const weatherInfo = this.extractWeatherInfo(itineraryData);
        document.getElementById('weatherInfo').textContent = weatherInfo;

        this.currentItinerary = itineraryData;
    }

    countTotalPOIs(dailyPlans) {
        let total = 0;
        dailyPlans.forEach(day => {
            if (day.pois) {
                total += day.pois.length;
            }
        });
        return total;
    }

    formatBudget(budgetEstimation) {
        if (budgetEstimation.total_min && budgetEstimation.total_max) {
            return `¥${budgetEstimation.total_min}-${budgetEstimation.total_max}`;
        } else if (budgetEstimation.total_min) {
            return `¥${budgetEstimation.total_min}+`;
        }
        return '待计算';
    }

    extractWeatherInfo(itineraryData) {
        // 从工具结果中提取天气信息
        if (itineraryData.weather_forecast && itineraryData.weather_forecast.length > 0) {
            const weather = itineraryData.weather_forecast[0];
            return weather.weather || weather.dayweather || '晴';
        }
        return '晴';
    }

    showAlert(message, type = 'info') {
        // 创建Bootstrap警告框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
    
    async saveItinerary() {
        if (!this.currentItinerary) return;
        
        try {
            // 这里可以实现保存逻辑
            this.showAlert('行程已保存', 'success');
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('行程已保存');
            }
        } catch (error) {
            this.showAlert('保存失败: ' + error.message, 'danger');
        }
    }
    
    editItinerary() {
        if (!this.currentItinerary) return;
        
        // 这里可以实现编辑逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }
    
    shareItinerary() {
        if (!this.currentItinerary) return;
        
        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${this.currentTraceId}`;
        
        if (navigator.share) {
            navigator.share({
                title: this.currentItinerary.title,
                text: this.currentItinerary.description,
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showAlert('分享链接已复制到剪贴板', 'success');
            });
        }
    }
    
    async loadUserHistory() {
        // 这里可以实现加载用户历史行程的逻辑
    }
    
    showHistory() {
        // 显示历史行程模态框
        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TravelPlannerAppRefactored();
});
