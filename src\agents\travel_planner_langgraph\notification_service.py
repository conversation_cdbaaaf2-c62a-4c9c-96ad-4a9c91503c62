"""
NotificationService - 实时推送与状态追踪服务

基于推送.md文档要求实现的核心通知服务，负责：
- 生成唯一的step_id
- 推送step_start和step_end事件
- 同步Redis状态
- 支持页面刷新后状态恢复
"""

import json
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import asyncio

logger = logging.getLogger(__name__)


class NotificationService:
    """
    实时通知服务
    
    为LangGraph的每个关键节点提供实时推送功能，
    支持SSE事件流和Redis状态同步。
    """
    
    def __init__(self, redis_client=None, session_id: str = None):
        """
        初始化通知服务
        
        Args:
            redis_client: Redis客户端（可选）
            session_id: 会话ID
        """
        self.redis_client = redis_client
        self.session_id = session_id or f"session_{int(datetime.now().timestamp())}"
        self.step_counter = 0
        self.active_steps: Dict[str, Dict[str, Any]] = {}
        
    def generate_step_id(self) -> str:
        """生成唯一的步骤ID"""
        self.step_counter += 1
        return f"{self.session_id}_step_{self.step_counter}_{uuid.uuid4().hex[:8]}"
    
    async def notify_step_start(
        self, 
        step_name: str, 
        title: str, 
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> tuple[str, str]:
        """
        推送步骤开始事件
        
        Args:
            step_name: 步骤名称，如'core_intent_analysis'
            title: 显示标题，如'解析用户需求和画像'
            message: 状态消息，如'正在分析您的旅行意图...'
            metadata: 额外的元数据
            
        Returns:
            tuple[step_id, sse_event]: 步骤ID和SSE事件字符串
        """
        step_id = self.generate_step_id()
        timestamp = datetime.now().isoformat()
        
        # 构建事件数据
        event_data = {
            "step_id": step_id,
            "step_name": step_name,
            "title": title,
            "message": message,
            "timestamp": timestamp
        }
        
        if metadata:
            event_data.update(metadata)
        
        # 构建SSE事件
        sse_event = {
            "event": "step_start",
            "data": event_data
        }
        
        # 记录活跃步骤
        self.active_steps[step_id] = {
            "step_name": step_name,
            "title": title,
            "start_time": timestamp,
            "status": "running"
        }
        
        # 同步到Redis
        await self._sync_to_redis(step_id, "start", event_data)
        
        # 格式化为SSE字符串
        sse_string = f"data: {json.dumps(sse_event, ensure_ascii=False)}\n\n"
        
        logger.info(f"步骤开始: {step_name} ({step_id}) - {title}")
        return step_id, sse_string
    
    async def notify_step_end(
        self,
        step_id: str,
        status: str = "success",
        result: Optional[Dict[str, Any]] = None,
        message: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        推送步骤结束事件
        
        Args:
            step_id: 步骤ID（由notify_step_start返回）
            status: 状态，'success'或'error'
            result: 步骤结果数据
            message: 结束消息
            metadata: 额外的元数据
            
        Returns:
            sse_event: SSE事件字符串
        """
        timestamp = datetime.now().isoformat()
        
        # 获取步骤信息
        step_info = self.active_steps.get(step_id, {})
        step_name = step_info.get("step_name", "unknown")
        
        # 构建事件数据
        event_data = {
            "step_id": step_id,
            "step_name": step_name,
            "status": status,
            "message": message,
            "timestamp": timestamp
        }
        
        if result:
            event_data["result"] = result
            
        if metadata:
            event_data.update(metadata)
        
        # 构建SSE事件
        sse_event = {
            "event": "step_end",
            "data": event_data
        }
        
        # 更新活跃步骤状态
        if step_id in self.active_steps:
            self.active_steps[step_id].update({
                "status": status,
                "end_time": timestamp,
                "result": result
            })
        
        # 同步到Redis
        await self._sync_to_redis(step_id, "end", event_data)
        
        # 格式化为SSE字符串
        sse_string = f"data: {json.dumps(sse_event, ensure_ascii=False)}\n\n"
        
        logger.info(f"步骤结束: {step_name} ({step_id}) - {status}")
        return sse_string
    
    async def notify_progress(
        self,
        step_id: str,
        progress: int,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        推送进度更新事件
        
        Args:
            step_id: 步骤ID
            progress: 进度百分比 (0-100)
            message: 进度消息
            details: 详细信息
            
        Returns:
            sse_event: SSE事件字符串
        """
        timestamp = datetime.now().isoformat()
        
        event_data = {
            "step_id": step_id,
            "progress": progress,
            "message": message,
            "timestamp": timestamp
        }
        
        if details:
            event_data["details"] = details
        
        sse_event = {
            "event": "step_progress",
            "data": event_data
        }
        
        # 同步到Redis
        await self._sync_to_redis(step_id, "progress", event_data)
        
        sse_string = f"data: {json.dumps(sse_event, ensure_ascii=False)}\n\n"
        return sse_string
    
    async def notify_completion(
        self,
        final_result: Dict[str, Any],
        message: str = "规划完成"
    ) -> str:
        """
        推送完成事件
        
        Args:
            final_result: 最终结果
            message: 完成消息
            
        Returns:
            sse_event: SSE事件字符串
        """
        timestamp = datetime.now().isoformat()
        
        event_data = {
            "session_id": self.session_id,
            "message": message,
            "result": final_result,
            "timestamp": timestamp
        }
        
        sse_event = {
            "event": "completed",
            "data": event_data
        }
        
        # 同步到Redis
        await self._sync_to_redis("completion", "completed", event_data)
        
        sse_string = f"data: {json.dumps(sse_event, ensure_ascii=False)}\n\n"
        
        logger.info(f"会话完成: {self.session_id}")
        return sse_string
    
    async def notify_error(
        self,
        error_message: str,
        step_id: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        推送错误事件
        
        Args:
            error_message: 错误消息
            step_id: 相关步骤ID（可选）
            error_details: 错误详情
            
        Returns:
            sse_event: SSE事件字符串
        """
        timestamp = datetime.now().isoformat()
        
        event_data = {
            "session_id": self.session_id,
            "error_message": error_message,
            "timestamp": timestamp
        }
        
        if step_id:
            event_data["step_id"] = step_id
            
        if error_details:
            event_data["error_details"] = error_details
        
        sse_event = {
            "event": "error",
            "data": event_data
        }
        
        # 同步到Redis
        await self._sync_to_redis(step_id or "error", "error", event_data)
        
        sse_string = f"data: {json.dumps(sse_event, ensure_ascii=False)}\n\n"
        
        logger.error(f"会话错误: {self.session_id} - {error_message}")
        return sse_string
    
    async def _sync_to_redis(
        self, 
        step_id: str, 
        event_type: str, 
        event_data: Dict[str, Any]
    ):
        """
        同步状态到Redis
        
        Args:
            step_id: 步骤ID
            event_type: 事件类型
            event_data: 事件数据
        """
        if not self.redis_client:
            return
            
        try:
            # Redis键名
            redis_key = f"travel_planner:session:{self.session_id}:step:{step_id}"
            
            # 保存事件数据
            redis_data = {
                "event_type": event_type,
                "event_data": event_data,
                "updated_at": datetime.now().isoformat()
            }
            
            # 异步保存到Redis
            await self.redis_client.hset(
                redis_key, 
                mapping={k: json.dumps(v, ensure_ascii=False) for k, v in redis_data.items()}
            )
            
            # 设置过期时间（24小时）
            await self.redis_client.expire(redis_key, 86400)
            
        except Exception as e:
            logger.warning(f"Redis同步失败: {str(e)}")
    
    async def get_session_state(self) -> Dict[str, Any]:
        """
        获取会话状态
        
        Returns:
            会话状态信息
        """
        if not self.redis_client:
            return {
                "session_id": self.session_id,
                "active_steps": self.active_steps,
                "redis_available": False
            }
        
        try:
            # 从Redis获取所有相关键
            pattern = f"travel_planner:session:{self.session_id}:step:*"
            keys = await self.redis_client.keys(pattern)
            
            session_data = {}
            for key in keys:
                step_data = await self.redis_client.hgetall(key)
                if step_data:
                    session_data[key] = {
                        k: json.loads(v) for k, v in step_data.items()
                    }
            
            return {
                "session_id": self.session_id,
                "active_steps": self.active_steps,
                "redis_data": session_data,
                "redis_available": True
            }
            
        except Exception as e:
            logger.warning(f"获取会话状态失败: {str(e)}")
            return {
                "session_id": self.session_id,
                "active_steps": self.active_steps,
                "redis_available": False,
                "error": str(e)
            }
